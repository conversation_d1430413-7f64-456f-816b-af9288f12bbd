import glob
import math
import numpy as np
import os
import pandas as pd
import pywt

from matplotlib import pyplot as plt

class PyPil:
    """
    Purpose: 
    This is the base class used to calculate three types of formulas 
    relating to pupil measurement with subclasses for each of the specific
    calculations. Since each of these calculations serve as an extension 
    of the previous one and as such is listed in the corresponding order 
    within the class and the only parts that are in this one are the items 
    shared by all three.
  
    The three papers and calculations in question and their subclass name:
    IPA:
    1) Index of Pupillary Activity (IPA) from <PERSON><PERSON><PERSON> et al. (2018)
    
    LHIPA:
    2) Low/High Index of Pupillary Activity (LHIPA) from <PERSON><PERSON><PERSON> et al. (2020)
    
    RIPA:
    3) Reat-Time-Index of Pupillary Activity (RIPA) from <PERSON><PERSON><PERSON> et al. (2022)
    
    ...
    
    Attributes:
    pupil_data : list
        The pupil diameter measurements in pixels
    wavelet : str
        The wavelet chosen for the decomposition (default: sym16)
    periodization : str
        The periodization chosen for the decomposition (default: per)
    level : int
        The level of granularity done within the decomposition (default: 2)
        
    ...
    
    Methods:
    calculate_timestamps()
        This returns the difference from the first and last point of measure.
   
    compute_modmax(cD2)
        
    
    compute_threshold(detect, mode="hard")
    
    calculate_ipa(cD2t)
    
    """
    def __init__(self, pupil_data, wavelet="sym16", periodization="per", level = 2, specification="world_timestamp"):
        try:
            import numpy as np
            import math
            import pywt
        except ImportError as e:
            raise ImportError("A required library is not imported, verify that numpy, math, and pywt are imported.")
        
        self.pupil_data = pupil_data
        self.pupil_data_diam = list(pupil_data['diameter'])
        self.pupil_data_time_WI = list(pupil_data['world_index'])
        self.pupil_data_time_WT = list(pupil_data['world_timestamp'])
        self.wavelet = wavelet
        self.periodization = periodization
        self.level = level
        self.specification = specification
        
    # NOTE: This one is changed quite a lot from the original version in the 
    # psuedocode. This is because it uses timestamp compared to what the data 
    # we have.
    
    # CONCERN: The dataframes did not have an "actual" timestamp option, so 
    # I had to make do with what I had and the "world_index" was the closest
    # to it.
    
    # CONCERN 2: The Lindlbauer paper wants us to use a 60 row window to calculate
    # the IPA and then to shift it from there, this is far easier said than done.
    def calculate_timestamps(self):
        """
        Purpose: 
        This calculates the difference in time from the last point and the first point.
        
        ...
        
        Features:
        No input features
        
        ...
        
        Output:
        This will output a numeric value that is the difference in time between the two points.
        
        """
        tt = self.pupil_data[self.specification].iloc[-1] - self.pupil_data[self.specification].iloc[0]
#         tt = self.pupil_data[self.specification].idxmax()  - self.pupil_data[self.specification].idxmin() 

#         try:
#             tt = self.pupil_data_time[-1] - self.pupil_data_time[0]
#         except:
#             tt = pupil_data.shape[0]
#         tt = self.pupil_data.shape[0]
#         print("\nTimestamp Calculation:")
#         print("tt:", tt)
        return tt
        
    def compute_modmax(self, d):
        """
        Purpose:
        This will find where the modulus is grater than both of its 
        neighbours. 
        
        ...
        
        Features:
        cD2 : array
            This is the normalization of Detail coefficients at level 2
        
        ...
        
        Output:
        detect : array
        
        
        """    
        m = [0.0] * len(d)
        for i in range(len(d)):
            m[i] = math.fabs(d[i])
            
        t = [0.0] * len(d)
        for i in range(len(d)):    
            ll = m[i-1] if i >= 1 else m[i]
            oo = m[i]
            rr = m[i+1] if i < len(d)-2 else m[i]
            
            if (ll <= oo and oo >= rr) and (ll < oo or oo > rr):
                t[i] = math.sqrt(d[i]**2)
            else:
                t[i] = 0.0
#         print("\nModmax Calculation:")
#         print(detect)
        return t


    # CONCERN: whether we do hard or softe thresholding, everything
    # turns to 0 with this for every single dataframe we put inside.
    # I suspect this is a problem with my calculations.
    def compute_threshold(self, detect, mode="hard"):
        """
        Purpose:
        
        ...
        
        Features:
        
        ...
        
        Output:
        
        
        """
        thresh = np.std(detect) * math.sqrt(2.0*np.log2(len(detect)))
        cD2t = pywt.threshold(detect,thresh,mode)
#         print("\nCalculate Threshold:")
#         print(cD2t)
        return cD2t
    
    def calculate_ipa(self, cD2t):
        """
        Purpose:
        
        ...
        
        Features:
        
        ...
        
        Output:
        
        
        """
        ctr = 0
        for i in range(len(cD2t)):
            if math.fabs(cD2t[i]) > 0: 
                ctr += 1
        ipa = float(ctr)/ self.calculate_timestamps()
#         print("\nIPA:")
#         print(ipa)
        return ipa

class IPA(PyPil):
    """
    Purpose: 
    This class calculates the Index of Pupillary Activity (IPA) based on pupil measurements.
    
    ...
    
    Attributes:
    pupil_data : list
        The pupil diameter measurements in pixels
    wavelet : str
        The wavelet chosen for the decomposition (default: sym16)
    periodization : str
        The periodization chosen for the decomposition (default: per)
    level : int
        The level of granularity done within the decomposition (default: 2)
    
    ...
    
    Methods:
    wavelet_decomposition()
        This returns the coefficients of the wavelet decomposition.
        
    normalize_coefficients(cA2, cD2, cD1, level)
        This normalizes the three coefficients w.r.t. the level deteremined
        at the start of the class.
        
    """
    def __init__(self, pupil_data, wavelet="sym16", periodization="per", level=2, specification="world_timestamp"):
        super().__init__(pupil_data, wavelet, periodization, level, specification)
        
        # Perform IPA calculation upon initialization
        cA2, cD2, cD1 = self.ipa_wavelet_decomposition()
        cA2[:], cD2[:], cD1[:] = self.ipa_normalize_coefficients(cA2, cD2, cD1, self.level)
        detect = self.compute_modmax(cD2[:])
        cD2t = self.compute_threshold(detect)
        self.ipa = self.calculate_ipa(cD2t)
#         print(f"IPA Calculation: {self.ipa}")

    def ipa_wavelet_decomposition(self):
        """
        Purpose: 
        This takes formula (0) to (4) from the paper and does it all with very minimal
        code. It takes sin the pupil diameter, uses sym16, per, and level 2 as its inputs 
        as they were listed in the original paper.
        
        ...
        
        Features:
        No input features
                
        ...
        
        Output:
        cA2 : array
            Approximation coefficients at level 2. These are coarse-grained
            details of the signal.
        cD2 : array
            Detail coefficients at level 2. These are high freq components.
        cD1 : array
            Detail coefficients at level 1. These are high freq components.
        
        """
        # This was created to look like the one on the pseudocode in the paper
        cA2, cD2, cD1 = pywt.wavedec(self.pupil_data_diam, self.wavelet, mode=self.periodization, level=self.level)
#             print("Wavelet Decomposition:")
#             print("\n", "cA2:",  cA2)
#             print("\n", "cD2:", cD2)
#             print("\n", "cD1:", cD1)
        return cA2, cD2, cD1 
        
    def ipa_normalize_coefficients(self, cA2, cD2, cD1, level):
        """
        Purpose:
        This will normalize the coefficients for each one we created. 
        
        ...
        
        Features:
        cA2 : array
            Approximation coefficients at level 2. These are coarse-grained
            details of the signal.
        cD2 : array
            Detail coefficients at level 2. These are high freq components.
        cD1 : array
            Detail coefficients at level 1. These are high freq components.
        level : int
            The level of granularity done within the decomposition (default: 2)
        
        ...
        
        Output:
        cA2[:] : array
            This is the normalization of Approximation coefficients at level 2
        cD2[:] : array
            This is the normalization of Detail coefficients at level 2
        cD1[:] : array
            This is the normalization of Detail coefficients at level 1
        
        """
        # normalize by 1/2 j , j = 2 for 2- level DWT
        # This is now changed to account for if we change the level
        cA2[:] = [x / math.sqrt(2.0 * level) for x in cA2] 
        cD2[:] = [x / math.sqrt(2.0 * level) for x in cD2]
        cD1[:] = [x / math.sqrt(1.0 * level) for x in cD1] 
        return cA2[:], cD2[:], cD1[:]        


class LHIPA(PyPil):
    """
    Purpose: 
    This class calculates the Low/High Index of Pupillary Activity (LHIPA) based on pupil measurements.
    
    ...
    
    Attributes:
    pupil_data : list
        The pupil diameter measurements in pixels
    wavelet : str
        The wavelet chosen for the decomposition (default: sym16)
    periodization : str
        The periodization chosen for the decomposition (default: per)
    level : int
        The level of granularity done within the decomposition (default: 2)
    
    ...
    
    Methods:
    wavelet_decomposition()
        This returns the coefficients of the wavelet decomposition.
        
    normalize_coefficients(cA2, cD2, cD1, level)
        This normalizes the three coefficients w.r.t. the level deteremined
        at the start of the class.
    
    """
    def __init__(self, pupil_data, wavelet="sym16", periodization="per", level=2, specification="world_timestamp"):
        super().__init__(pupil_data, wavelet, periodization, level, specification)
        
      
        # Perform LHIPA calculation upon initialization
        self.max_level = self.lhipa_wavelet_decomposition(self.pupil_data_diam)
        self.hif, self.lof = 1, int(self.max_level/2)
        cD_H, cD_L  = self.lhipa_normalize_coefficients(self.pupil_data_diam, self.max_level)
        cD_LH = self.lhipa_ratio(cD_H[:], cD_L[:])
        cD_LHm = self.compute_modmax(cD_LH)
        cD_LHt = self.compute_threshold(cD_LHm, mode="less")
        self.lhipa = self.calculate_ipa(cD_LHt)
#         print(f"LHIPA Calculation: {self.lhipa}")
        
    def lhipa_wavelet_decomposition(self, d):
        """
        Purpose:
        
        ...
        
        Features:
        
        ...
        
        Output:
        
        
        """
        w = pywt.Wavelet('sym16')
        self.max_level = pywt.dwt_max_level(len(d),filter_len=w.dec_len)
#         print("\n", "Max Level:")
#         print("\n", max_level)
        return self.max_level
        
    def lhipa_normalize_coefficients(self, d, max_level):
        """
        Purpose:

        
        ...
        
        Features:

        
        ...
        
        Output:

        
        """
        cD_H = pywt.downcoef('d',self.pupil_data_diam, 'sym16', 'per', level=self.hif)
        cD_L = pywt.downcoef('d',self.pupil_data_diam, 'sym16', 'per', level=self.lof)
        cD_H[:] = [x / math.sqrt(2**self.hif) for x in cD_H]
        cD_L[:] = [x / math.sqrt(2**self.lof) for x in cD_L]
        return cD_H[:], cD_L[:]   
    
    def lhipa_ratio(self, cD_H, cD_L):
        """
        Purpose:

        
        ...
        
        Features:

        
        ...
        
        Output:

        
        """
        cD_LH = cD_L.copy()
        for i in range(len(cD_L)):
            den = cD_H[((2**self.lof)//(2**self.hif))*i]
            if den != 0:
                cD_LH[i] = cD_L[i] / den
            else: 
                cD_LH[i] = cD_L[i] / 0.00000000001
        return cD_LH


# This is the version where the ONLY thing we did to the data was remove NaNs/Nulls
# and we did nothing else to it.
os.chdir(r"C:\Users\<USER>\Desktop\Semester 5, 2023\Eye Tracking\Project\New Project Content\Dataframes\trimmed_files")
trim_csv_files = glob.glob("*.csv")
trim_csv_files

ipa_list = []
lhipa_list = []

for csv in trim_csv_files:
    print(f"\nName of File: {csv}")
    
    df = pd.read_csv(csv)
    ipa = IPA(df)
    lhipa = LHIPA(df)
    ipa_list.append(ipa.ipa)
    lhipa_list.append(lhipa.lhipa)
    print("\n")

# This is the version where we assumed that each data point was measured more than one time
# and we are averaging those measurements out.

# os.chdir(r"C:\Users\<USER>\Desktop\Semester 5, 2023\Eye Tracking\Project\New Project Content\Dataframes\grouped_trimmed_folder")
# grouped_trim_csv_files = glob.glob("*.csv")
# grouped_trim_csv_files

# g_ipa_list = []
# g_lhipa_list = []

# for csv in grouped_trim_csv_files:
#     print(f"\nName of File: {csv}")
    
#     df = pd.read_csv(csv)
#     ipa = IPA(df, specification="world_index")
#     lhipa = LHIPA(df, specification="world_index")
#     g_ipa_list.append(ipa.ipa)
#     g_lhipa_list.append(lhipa.lhipa)
#     print("\n")

df = pd.read_csv('study01_p02-pupil_positions-trimmed-02_trimmed.csv')

df.head(5), df.tail(5)

df['world_timestamp'].iloc[-1] - df['world_timestamp'].iloc[0]

df['world_timestamp'].iloc[7001] - df['world_timestamp'].iloc[0]

ipa_list = []
lhipa_list = []

for csv in trim_csv_files:
    print(f"\nName of File: {csv}")
    
    df = pd.read_csv(csv)
    ipa = IPA(df)
    lhipa = LHIPA(df)
    ipa_list.append(ipa.ipa)
    lhipa_list.append(lhipa.lhipa)
    print("\n")

def shifting_window_processing(df, window_size=7000):
    ipa_list = []
    lhipa_list = []
    # Iterate through the shifting window
    for i in range(len(df) - window_size + 1):
        subset = df.iloc[i:i+window_size]
        ipa = IPA(subset)  
        lhipa = LHIPA(subset) 
        ipa_list.append(ipa.ipa)
        lhipa_list.append(lhipa.lhipa)
#         print("\n")
    return ipa_list, lhipa_list

ipa_list, lhipa_list = shifting_window_processing(df)

index = list(range(len(ipa_list)))

# Plot the lines
plt.plot(index, ipa_list, label='IPA')
plt.plot(index, lhipa_list, label='LHIPA')

# Add labels and legend
plt.xlabel('Index')
plt.ylabel('Values')
plt.title('IPA vs LHIPA')
plt.legend()

# Show plot
plt.show()



for i in trim_csv_files:
    df = pd.read_csv(i)
    ipa_list, lhipa_list = shifting_window_processing(df)
    
    index = list(range(len(ipa_list)))

    # Plot the lines
    plt.plot(index, ipa_list, label='IPA')
    plt.plot(index, lhipa_list, label='LHIPA')

    # Add labels and legend
    plt.title(trim_csv_files)
    plt.xlabel('Index')
    plt.ylabel('Values')
    plt.title('IPA vs LHIPA')
    plt.legend()

    # Show plot
    plt.show()





!pip freeze > requirements.txt