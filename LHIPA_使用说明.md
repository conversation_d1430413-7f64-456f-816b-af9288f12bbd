# LHIPA瞳孔测量系统使用说明

## 项目概述

本项目基于<PERSON><PERSON><PERSON>等人的研究论文，实现了瞳孔活动指数(IPA)和低/高频瞳孔活动指数(LHIPA)的计算。这些指标可以用于评估认知负荷和瞳孔活动模式。

### 核心算法

1. **IPA (Index of Pupillary Activity)** - 基于<PERSON> et al. (2018)
2. **LHIPA (Low/High Index of Pupillary Activity)** - 基于<PERSON> et al. (2020)
3. **RIPA (Real-Time Index of Pupillary Activity)** - 理论实现，基于Jayawardena et al. (2022)

## 文件结构介绍

### Pupillometric-measures-of-cognitive-load-main/
```
├── Instructions.pdf                    # 项目说明文档
├── README.md                          # 项目概述
├── Maintained Code/                   # 维护的代码
│   ├── PyPil.ipynb                   # 主要实现（Jupyter Notebook）
│   ├── Data_Preprocessor.py          # 数据预处理器
│   ├── Data_Preprocessor.ipynb       # 数据预处理器（Notebook版本）
│   ├── PyPil_requirements.txt        # PyPil依赖包
│   └── Data_Preprocessor_requirements.txt # 预处理器依赖包
├── Proposal/                          # 研究提案和论文
│   ├── Chris_Hyek_First_Proposal.pdf
│   └── Eye_Tracking_Paper-Final.pdf
└── Unmaintained Code/                 # 未维护的代码
    ├── Initial Preprocessing Test.ipynb
    └── Project Implementation v1.ipynb
```

### 您的预处理模块
```
analysis/
└── preprocess_edf.py                 # 您的EDF预处理模块
```

## 核心类和方法

### 1. PyPil基类
- **功能**: 提供共享的基础方法
- **主要方法**:
  - `calculate_timestamps()`: 计算时间戳差值
  - `compute_modmax()`: 计算模极大值
  - `compute_threshold()`: 计算阈值
  - `calculate_ipa()`: 计算IPA值

### 2. IPA子类
- **功能**: 计算标准的瞳孔活动指数
- **主要方法**:
  - `ipa_wavelet_decomposition()`: 小波分解
  - `ipa_normalize_coefficients()`: 系数归一化

### 3. LHIPA子类
- **功能**: 计算低/高频瞳孔活动指数
- **主要方法**:
  - `lhipa_wavelet_decomposition()`: LHIPA小波分解
  - `lhipa_normalize_coefficients()`: LHIPA系数归一化
  - `lhipa_ratio()`: 计算低/高频比率

## 数据格式要求

### PyPil类期望的数据格式
```python
# DataFrame必须包含以下列：
- 'diameter': 瞳孔直径数据
- 'world_index': 世界索引（整数）
- 'world_timestamp': 世界时间戳（浮点数）
```

### 您的预处理数据格式
```python
# 您的preprocess_edf.py输出包含：
- 'time': 时间戳（毫秒）
- 'pa_left': 左眼瞳孔面积
- 'pa_right': 右眼瞳孔面积
- 'pupil_avg': 双眼平均瞳孔数据
```

## 安装和环境配置

### 必需的Python包
```bash
# 核心依赖
pip install numpy pandas matplotlib
pip install pywt  # PyWavelets for wavelet analysis
pip install scipy  # for interpolation

# 可选依赖（用于完整功能）
pip install pyedfread  # for EDF file reading
pip install glob os warnings  # 通常已内置
```

### 最小依赖安装
```bash
# 仅安装LHIPA计算所需的包
pip install numpy pandas pywt math
```

## 使用方法

### 方法1: 直接使用您的预处理数据

```python
import sys
sys.path.append('Pupillometric-measures-of-cognitive-load-main/Maintained Code')

# 导入PyPil类
from PyPil import IPA, LHIPA
import pandas as pd
import numpy as np

# 1. 使用您的预处理模块
from analysis.preprocess_edf import preprocess_edf_file

# 2. 预处理EDF文件
edf_path = "your_file.edf"
processed_data = preprocess_edf_file(edf_path)

# 3. 转换数据格式以适配PyPil
def convert_to_pypil_format(processed_data):
    """将您的预处理数据转换为PyPil格式"""
    pypil_data = pd.DataFrame()
    
    # 使用双眼平均数据作为直径
    pypil_data['diameter'] = processed_data['pupil_avg']
    
    # 创建world_index（简单的序列索引）
    pypil_data['world_index'] = range(len(processed_data))
    
    # 转换时间戳（从毫秒转换）
    pypil_data['world_timestamp'] = processed_data['time'] / 1000.0
    
    # 移除NaN值
    pypil_data = pypil_data.dropna()
    
    return pypil_data

# 4. 转换数据格式
pypil_data = convert_to_pypil_format(processed_data)

# 5. 计算IPA和LHIPA
ipa_calculator = IPA(pypil_data)
lhipa_calculator = LHIPA(pypil_data)

# 6. 获取结果
ipa_value = ipa_calculator.ipa
lhipa_value = lhipa_calculator.lhipa

print(f"IPA值: {ipa_value}")
print(f"LHIPA值: {lhipa_value}")
```

### 方法2: 使用滑动窗口计算实时LHIPA

```python
def calculate_sliding_lhipa(processed_data, window_size=7000, step_size=1000):
    """
    使用滑动窗口计算实时LHIPA系数
    
    Args:
        processed_data: 预处理后的数据
        window_size: 窗口大小（数据点数）
        step_size: 步长（数据点数）
    
    Returns:
        list: LHIPA值列表
    """
    lhipa_values = []
    ipa_values = []
    
    for i in range(0, len(processed_data) - window_size + 1, step_size):
        # 提取窗口数据
        window_data = processed_data.iloc[i:i+window_size]
        
        # 转换格式
        pypil_data = convert_to_pypil_format(window_data)
        
        if len(pypil_data) > 100:  # 确保有足够的数据点
            try:
                # 计算LHIPA
                lhipa_calc = LHIPA(pypil_data)
                ipa_calc = IPA(pypil_data)
                
                lhipa_values.append(lhipa_calc.lhipa)
                ipa_values.append(ipa_calc.ipa)
            except Exception as e:
                print(f"窗口 {i} 计算失败: {e}")
                lhipa_values.append(np.nan)
                ipa_values.append(np.nan)
    
    return ipa_values, lhipa_values

# 使用示例
ipa_series, lhipa_series = calculate_sliding_lhipa(processed_data)
```

## 参数配置

### PyPil类参数
```python
# 创建IPA实例时的参数
ipa = IPA(
    pupil_data=your_data,           # 必需：瞳孔数据DataFrame
    wavelet="sym16",                # 小波类型（默认：sym16）
    periodization="per",            # 周期化方法（默认：per）
    level=2,                        # 分解层数（默认：2）
    specification="world_timestamp" # 时间戳列名（默认：world_timestamp）
)

# LHIPA使用相同的参数
lhipa = LHIPA(pupil_data=your_data, ...)
```

### 您的预处理参数
```python
# 在preprocess_edf.py中的参数
processed_data = preprocess_edf_file(
    edf_path,
    smooth_window=5,                    # 滑动窗口大小
    blink_interpolation_window=40,      # 眨眼插值窗口（毫秒）
    interpolation_method='quadratic',   # 插值方法
    velocity_threshold=500.0,           # 速度阈值
    eye_selection='binocular'           # 眼睛选择
)
```

## 输出结果解释

### IPA值
- **范围**: 通常在0-1之间
- **含义**: 瞳孔活动的频率，值越高表示瞳孔变化越频繁
- **应用**: 认知负荷评估，注意力状态监测

### LHIPA值
- **范围**: 通常大于1
- **含义**: 低频与高频瞳孔活动的比率
- **应用**: 自主神经系统平衡评估，情绪状态分析

## 常见问题和解决方案

### 1. 数据格式不匹配
**问题**: PyPil期望特定的列名
**解决**: 使用`convert_to_pypil_format()`函数转换

### 2. 计算结果为NaN
**问题**: 数据质量不足或窗口太小
**解决**: 增加窗口大小，检查数据预处理质量

### 3. 内存不足
**问题**: 处理大文件时内存溢出
**解决**: 使用滑动窗口分段处理

### 4. 依赖包缺失
**问题**: ImportError
**解决**: 安装必需的包，特别是PyWavelets

## 性能优化建议

1. **数据预处理**: 在计算LHIPA前确保数据质量
2. **窗口大小**: 根据采样率调整窗口大小（建议60秒数据）
3. **批处理**: 对多个文件使用批处理模式
4. **缓存**: 利用您的预处理模块的缓存功能

## 下一步计划

根据您的需求，我们将按以下顺序进行：

1. **测试环境搭建** - 安装必要依赖
2. **运行示例代码** - 验证PyPil功能
3. **数据格式适配** - 连接您的预处理模块
4. **实时计算实现** - 实现滑动窗口LHIPA
5. **性能优化** - 提高计算效率
6. **结果验证** - 与论文结果对比
